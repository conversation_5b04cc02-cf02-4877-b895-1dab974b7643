<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高科技个人介绍</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00f5ff;
            --secondary-color: #ff0080;
            --accent-color: #39ff14;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Orbitron', monospace;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 50%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(57, 255, 20, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .theme-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid var(--primary-color);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(20px);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }
        
        .theme-selector-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            color: var(--text-primary);
            font-size: 0.9rem;
        }
        
        .theme-select {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--primary-color);
            color: var(--text-primary);
            font-family: 'Orbitron', monospace;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
        }
        
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .scroll-indicator {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: var(--text-primary);
            opacity: 0.8;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        .scroll-arrow {
            width: 24px;
            height: 24px;
            border: 2px solid var(--primary-color);
            border-top: none;
            border-left: none;
            transform: rotate(45deg);
            margin-bottom: 5px;
            animation: arrow-glow 2s ease-in-out infinite alternate;
        }

        @keyframes arrow-glow {
            0% {
                border-color: var(--primary-color);
                box-shadow: 0 0 5px var(--primary-color);
            }
            100% {
                border-color: var(--accent-color);
                box-shadow: 0 0 15px var(--accent-color);
            }
        }

        .scroll-text {
            font-size: 0.9rem;
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--text-secondary);
            text-align: center;
        }
        
        .avatar-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .avatar-img {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            object-fit: cover;
            animation: avatar-pulse 3s ease-in-out infinite;
        }
        
        @keyframes avatar-pulse {
            0%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            }
            50% { 
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
            }
        }
        
        .name {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Orbitron', monospace;
        }
        
        .glitch {
            position: relative;
            animation: glitch 3s infinite;
        }
        
        .glitch::before,
        .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .glitch::before {
            animation: glitch-1 0.3s infinite linear alternate-reverse;
            color: var(--secondary-color);
            z-index: -1;
            clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%);
        }
        
        .glitch::after {
            animation: glitch-2 0.3s infinite linear alternate-reverse;
            color: var(--accent-color);
            z-index: -2;
            clip-path: polygon(0 65%, 100% 65%, 100% 100%, 0 100%);
        }
        
        @keyframes glitch {
            0%, 100% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
        }
        
        @keyframes glitch-1 {
            0% { transform: translate(0); }
            20% { transform: translate(-2px, 2px); }
            40% { transform: translate(-2px, -2px); }
            60% { transform: translate(2px, 2px); }
            80% { transform: translate(2px, -2px); }
            100% { transform: translate(0); }
        }
        
        @keyframes glitch-2 {
            0% { transform: translate(0); }
            20% { transform: translate(2px, -2px); }
            40% { transform: translate(2px, 2px); }
            60% { transform: translate(-2px, -2px); }
            80% { transform: translate(-2px, 2px); }
            100% { transform: translate(0); }
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }
        
        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .section {
            padding: 4rem 0;
        }
        
        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Orbitron', monospace;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .skill-category {
            background: rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 245, 255, 0.3);
            border-radius: 15px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .skill-category:hover {
            border-color: var(--primary-color);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            transform: translateY(-5px);
        }
        
        .skill-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 12px;
        }
        
        .skill-icon {
            font-size: 1.5rem;
            margin-right: 1rem;
            width: 40px;
            text-align: center;
        }
        
        .skill-info {
            flex: 1;
        }
        
        .skill-name {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .skill-progress {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .skill-progress-bar {
            height: 100%;
            border-radius: 4px;
            transition: width 1s ease-in-out;
        }
        
        .skill-level {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-left: 1rem;
        }
        
        @media (max-width: 768px) {
            .name {
                font-size: 2.5rem;
            }
            
            .avatar-img {
                width: 120px;
                height: 120px;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-selector {
                top: 10px;
                right: 10px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 主题选择器 -->
    <div class="theme-selector">
        <div class="theme-selector-label">
            <i class="fas fa-palette"></i>
            <span>主题</span>
        </div>
        <select id="theme-select" class="theme-select">
            <option value="cyberpunk">赛博朋克</option>
            <option value="matrix">黑客帝国</option>
            <option value="neon">霓虹科技</option>
            <option value="quantum">量子蓝</option>
        </select>
    </div>

    <!-- 主页部分 -->
    <section class="hero">
        <div class="container">
            <div class="avatar-container">
                <img id="avatar" src="" alt="头像" class="avatar-img">
            </div>
            <h1 id="name" class="name glitch" data-text="">加载中...</h1>
            <p id="title" class="hero-subtitle">加载中...</p>
            <p id="bio" class="hero-description">加载中...</p>
        </div>

        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
            <span class="scroll-text">向下滚动</span>
        </div>
    </section>

    <!-- 技能部分 -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">技能专长</h2>
            <div id="skills-container" class="skills-grid">
                <!-- 技能内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <script>
        let profileData = {};
        
        // 加载数据
        async function loadData() {
            try {
                console.log('Loading profile data...');
                const response = await fetch('profile.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                profileData = await response.json();
                console.log('Data loaded successfully');
                renderProfile();
                initializeThemeSelector();
            } catch (error) {
                console.error('Failed to load data:', error);
                useDefaultData();
            }
        }
        
        function useDefaultData() {
            profileData = {
                personal: {
                    name: "开发者",
                    title: "全栈工程师",
                    bio: "热爱技术的开发者",
                    avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"
                },
                skills: [{
                    category: "编程语言",
                    items: [
                        { name: "JavaScript", level: 90, icon: "⚡", color: "#f7df1e" },
                        { name: "Python", level: 85, icon: "🐍", color: "#3776ab" },
                        { name: "Java", level: 80, icon: "☕", color: "#f89820" }
                    ]
                }],
                themes: {
                    cyberpunk: {
                        name: "赛博朋克",
                        colors: {
                            primary: "#00f5ff",
                            secondary: "#ff0080",
                            accent: "#39ff14",
                            background: "#0a0a0a",
                            surface: "#1a1a1a",
                            text: "#ffffff",
                            textSecondary: "#b0b0b0"
                        }
                    },
                    matrix: {
                        name: "黑客帝国",
                        colors: {
                            primary: "#00ff41",
                            secondary: "#008f11",
                            accent: "#39ff14",
                            background: "#000000",
                            surface: "#001100",
                            text: "#00ff41",
                            textSecondary: "#008f11"
                        }
                    }
                }
            };
            renderProfile();
            initializeThemeSelector();
        }
        
        // 渲染个人信息
        function renderProfile() {
            const { personal, skills } = profileData;
            
            document.getElementById('avatar').src = personal.avatar;
            document.getElementById('name').textContent = personal.name;
            document.getElementById('name').setAttribute('data-text', personal.name);
            document.getElementById('title').textContent = personal.title;
            document.getElementById('bio').textContent = personal.bio;
            
            // 渲染技能
            renderSkills(skills);
        }
        
        function renderSkills(skills) {
            const skillsContainer = document.getElementById('skills-container');
            skillsContainer.innerHTML = '';
            
            skills.forEach(category => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'skill-category';
                categoryDiv.innerHTML = `
                    <h3>${category.category}</h3>
                    ${category.items.map(skill => `
                        <div class="skill-item">
                            <div class="skill-icon">${skill.icon}</div>
                            <div class="skill-info">
                                <div class="skill-name">${skill.name}</div>
                                <div class="skill-progress">
                                    <div class="skill-progress-bar" 
                                         style="background: ${skill.color}; width: 0%"
                                         data-level="${skill.level}">
                                    </div>
                                </div>
                            </div>
                            <div class="skill-level">${skill.level}%</div>
                        </div>
                    `).join('')}
                `;
                skillsContainer.appendChild(categoryDiv);
            });
            
            // 延迟执行技能条动画
            setTimeout(animateSkillBars, 500);
        }
        
        function animateSkillBars() {
            const skillBars = document.querySelectorAll('.skill-progress-bar');
            skillBars.forEach(bar => {
                const level = bar.getAttribute('data-level');
                bar.style.width = level + '%';
            });
        }
        
        // 主题切换
        function initializeThemeSelector() {
            const themeSelect = document.getElementById('theme-select');
            const savedTheme = localStorage.getItem('selectedTheme') || 'cyberpunk';
            
            themeSelect.value = savedTheme;
            applyTheme(savedTheme);
            
            themeSelect.addEventListener('change', function() {
                const themeName = this.value;
                applyTheme(themeName);
                localStorage.setItem('selectedTheme', themeName);
            });
        }
        
        function applyTheme(themeName) {
            if (!profileData.themes || !profileData.themes[themeName]) {
                console.error('Theme not found:', themeName);
                return;
            }
            
            const theme = profileData.themes[themeName];
            const root = document.documentElement;
            
            // 应用颜色
            Object.entries(theme.colors).forEach(([key, value]) => {
                const cssVar = '--' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
                root.style.setProperty(cssVar, value);
            });
            
            console.log('Applied theme:', themeName);
        }
        
        // 启动
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
