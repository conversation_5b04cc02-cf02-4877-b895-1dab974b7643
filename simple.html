<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版个人介绍</title>
    <style>
        :root {
            --primary-color: #00f5ff;
            --secondary-color: #ff0080;
            --accent-color: #39ff14;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .theme-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(26, 26, 26, 0.9);
            border: 1px solid var(--primary-color);
            border-radius: 10px;
            padding: 15px;
        }
        
        .theme-select {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--primary-color);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 5px;
        }
        
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            margin-bottom: 2rem;
        }
        
        .name {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        .title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }
        
        .section {
            padding: 4rem 0;
        }
        
        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: var(--primary-color);
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .skill-category {
            background: var(--bg-secondary);
            padding: 2rem;
            border-radius: 15px;
            border: 1px solid var(--primary-color);
        }
        
        .skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .skill-progress {
            width: 60%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .skill-progress-bar {
            height: 100%;
            background: var(--primary-color);
            transition: width 1s ease;
        }
    </style>
</head>
<body>
    <!-- 主题选择器 -->
    <div class="theme-selector">
        <select id="theme-select" class="theme-select">
            <option value="cyberpunk">赛博朋克</option>
            <option value="matrix">黑客帝国</option>
            <option value="neon">霓虹科技</option>
            <option value="quantum">量子蓝</option>
        </select>
    </div>

    <!-- 主页部分 -->
    <section class="hero">
        <div class="container">
            <img id="avatar" src="" alt="头像" class="avatar">
            <h1 id="name" class="name">加载中...</h1>
            <p id="title" class="title">加载中...</p>
            <p id="bio">加载中...</p>
        </div>
    </section>

    <!-- 技能部分 -->
    <section class="section">
        <div class="container">
            <h2 class="section-title">技能专长</h2>
            <div id="skills-container" class="skills-grid">
                <!-- 技能内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <script>
        let profileData = {};
        
        // 加载数据
        async function loadData() {
            try {
                console.log('Loading profile data...');
                const response = await fetch('profile.json');
                profileData = await response.json();
                console.log('Data loaded:', profileData);
                renderProfile();
                initializeThemeSelector();
            } catch (error) {
                console.error('Failed to load data:', error);
                // 使用默认数据
                profileData = {
                    personal: {
                        name: "开发者",
                        title: "全栈工程师",
                        bio: "热爱技术的开发者",
                        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face"
                    },
                    skills: [{
                        category: "编程语言",
                        items: [
                            { name: "JavaScript", level: 90 },
                            { name: "Python", level: 85 }
                        ]
                    }],
                    themes: {
                        cyberpunk: {
                            name: "赛博朋克",
                            colors: {
                                primary: "#00f5ff",
                                secondary: "#ff0080",
                                accent: "#39ff14",
                                background: "#0a0a0a",
                                surface: "#1a1a1a",
                                text: "#ffffff",
                                textSecondary: "#b0b0b0"
                            }
                        }
                    }
                };
                renderProfile();
                initializeThemeSelector();
            }
        }
        
        // 渲染个人信息
        function renderProfile() {
            const { personal, skills } = profileData;
            
            document.getElementById('avatar').src = personal.avatar;
            document.getElementById('name').textContent = personal.name;
            document.getElementById('title').textContent = personal.title;
            document.getElementById('bio').textContent = personal.bio;
            
            // 渲染技能
            const skillsContainer = document.getElementById('skills-container');
            skillsContainer.innerHTML = '';
            
            skills.forEach(category => {
                const categoryDiv = document.createElement('div');
                categoryDiv.className = 'skill-category';
                categoryDiv.innerHTML = `
                    <h3>${category.category}</h3>
                    ${category.items.map(skill => `
                        <div class="skill-item">
                            <span>${skill.name}</span>
                            <div class="skill-progress">
                                <div class="skill-progress-bar" style="width: ${skill.level}%"></div>
                            </div>
                            <span>${skill.level}%</span>
                        </div>
                    `).join('')}
                `;
                skillsContainer.appendChild(categoryDiv);
            });
        }
        
        // 主题切换
        function initializeThemeSelector() {
            const themeSelect = document.getElementById('theme-select');
            
            themeSelect.addEventListener('change', function() {
                const themeName = this.value;
                applyTheme(themeName);
            });
            
            // 应用默认主题
            applyTheme('cyberpunk');
        }
        
        function applyTheme(themeName) {
            if (!profileData.themes || !profileData.themes[themeName]) {
                console.error('Theme not found:', themeName);
                return;
            }
            
            const theme = profileData.themes[themeName];
            const root = document.documentElement;
            
            // 应用颜色
            Object.entries(theme.colors).forEach(([key, value]) => {
                root.style.setProperty(`--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`, value);
            });
            
            console.log('Applied theme:', themeName);
        }
        
        // 启动
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
