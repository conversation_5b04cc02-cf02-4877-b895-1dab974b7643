<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简介 - 全栈开发工程师</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 粒子背景容器 -->
    <div id="particles-container"></div>

    <!-- 科技感装饰线条 -->
    <div class="tech-lines">
        <div class="tech-line tech-line-1"></div>
        <div class="tech-line tech-line-2"></div>
        <div class="tech-line tech-line-3"></div>
        <div class="tech-line tech-line-4"></div>
    </div>

    <!-- 主题选择器 -->
    <div class="theme-selector">
        <div class="theme-selector-label">
            <i class="fas fa-palette"></i>
            <span>主题</span>
        </div>
        <select id="theme-select" class="theme-select">
            <option value="cyberpunk">赛博朋克</option>
            <option value="matrix">黑客帝国</option>
            <option value="neon">霓虹科技</option>
            <option value="quantum">量子蓝</option>
            <option value="synthwave">合成波</option>
            <option value="terminal">终端绿</option>
            <option value="arctic">极地蓝</option>
            <option value="fire">烈焰红</option>
        </select>
    </div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text cyber-text" data-text="Portfolio">Portfolio</span>
                <div class="logo-circuit"></div>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">首页</a></li>
                <li><a href="#about" class="nav-link">关于我</a></li>
                <li><a href="#skills" class="nav-link">技能</a></li>
                <li><a href="#experience" class="nav-link">经历</a></li>
                <li><a href="#projects" class="nav-link">项目</a></li>
                <li><a href="#contact" class="nav-link">联系</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主页部分 -->
    <section id="home" class="hero">
        <!-- 全息投影效果 -->
        <div class="hologram-overlay"></div>

        <!-- 数字雨效果容器 -->
        <div class="matrix-rain"></div>

        <div class="hero-container">
            <div class="hero-content">
                <!-- 科技感装饰框 -->
                <div class="tech-frame">
                    <div class="tech-corner tech-corner-tl"></div>
                    <div class="tech-corner tech-corner-tr"></div>
                    <div class="tech-corner tech-corner-bl"></div>
                    <div class="tech-corner tech-corner-br"></div>
                </div>

                <div class="hero-avatar">
                    <div class="avatar-container">
                        <img id="avatar" src="" alt="头像" class="avatar-img">
                        <div class="avatar-ring"></div>
                        <div class="avatar-glow"></div>
                        <div class="avatar-scan"></div>
                    </div>
                </div>

                <h1 class="hero-title">
                    <span class="greeting typewriter">你好，我是</span>
                    <span id="name" class="name glitch neon-text" data-text=""></span>
                </h1>
                <p id="title" class="hero-subtitle cyber-text"></p>
                <p id="subtitle" class="hero-description"></p>

                <!-- 状态指示器 -->
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="status-text">在线状态</span>
                </div>

                <div class="hero-buttons">
                    <a href="#contact" class="btn btn-primary cyber-btn">
                        <span class="btn-text">联系我</span>
                        <div class="btn-glow"></div>
                    </a>
                    <a href="#projects" class="btn btn-secondary cyber-btn">
                        <span class="btn-text">查看作品</span>
                        <div class="btn-glow"></div>
                    </a>
                </div>

                <div class="social-links">
                    <a id="github-link" href="#" class="social-link" target="_blank">
                        <i class="fab fa-github"></i>
                        <div class="social-ripple"></div>
                    </a>
                    <a id="linkedin-link" href="#" class="social-link" target="_blank">
                        <i class="fab fa-linkedin"></i>
                        <div class="social-ripple"></div>
                    </a>
                    <a id="blog-link" href="#" class="social-link" target="_blank">
                        <i class="fas fa-blog"></i>
                        <div class="social-ripple"></div>
                    </a>
                    <a id="wechat-link" href="#" class="social-link" target="_blank">
                        <i class="fab fa-weixin"></i>
                        <div class="social-ripple"></div>
                    </a>
                </div>
            </div>
        </div>

        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
            <span class="scroll-text">向下滚动</span>
        </div>
    </section>

    <!-- 关于我部分 -->
    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title glitch" data-text="关于我">关于我</h2>
            <div class="about-content">
                <div class="about-text">
                    <p id="bio" class="bio-text typewriter-bio"></p>
                    <div class="about-stats">
                        <div class="stat-item cyber-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <span class="stat-number counter" data-target="5">0</span>
                            <span class="stat-label">年经验</span>
                            <div class="stat-glow"></div>
                        </div>
                        <div class="stat-item cyber-card">
                            <div class="stat-icon">
                                <i class="fas fa-project-diagram"></i>
                            </div>
                            <span class="stat-number counter" data-target="50">0</span>
                            <span class="stat-label">项目完成</span>
                            <div class="stat-glow"></div>
                        </div>
                        <div class="stat-item cyber-card">
                            <div class="stat-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <span class="stat-number counter" data-target="10">0</span>
                            <span class="stat-label">技术栈</span>
                            <div class="stat-glow"></div>
                        </div>
                        <div class="stat-item cyber-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="stat-number counter" data-target="100">0</span>
                            <span class="stat-label">满意客户</span>
                            <div class="stat-glow"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 技能部分 -->
    <section id="skills" class="skills">
        <div class="container">
            <h2 class="section-title neon-text">技能专长</h2>
            <div id="skills-container" class="skills-grid">
                <!-- 技能内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 经历部分 -->
    <section id="experience" class="experience">
        <div class="container">
            <h2 class="section-title cyber-text">工作经历</h2>
            <div id="experience-container" class="timeline">
                <!-- 经历内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 项目部分 -->
    <section id="projects" class="projects">
        <div class="container">
            <h2 class="section-title glitch" data-text="精选项目">精选项目</h2>
            <div id="projects-container" class="projects-grid">
                <!-- 项目内容将通过JavaScript动态生成 -->
            </div>
        </div>
    </section>

    <!-- 联系部分 -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title neon-text">联系我</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item cyber-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <span class="contact-label">邮箱</span>
                            <span id="email" class="contact-value"></span>
                        </div>
                        <div class="contact-glow"></div>
                    </div>
                    <div class="contact-item cyber-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <span class="contact-label">电话</span>
                            <span id="phone" class="contact-value"></span>
                        </div>
                        <div class="contact-glow"></div>
                    </div>
                    <div class="contact-item cyber-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <span class="contact-label">位置</span>
                            <span id="location" class="contact-value"></span>
                        </div>
                        <div class="contact-glow"></div>
                    </div>
                </div>

                <!-- 社交媒体链接 -->
                <div class="social-links">
                    <a href="#" class="social-link" data-platform="github">
                        <i class="fab fa-github"></i>
                        <span>GitHub</span>
                        <div class="social-glow"></div>
                    </a>
                    <a href="#" class="social-link" data-platform="linkedin">
                        <i class="fab fa-linkedin"></i>
                        <span>LinkedIn</span>
                        <div class="social-glow"></div>
                    </a>
                    <a href="#" class="social-link" data-platform="wechat">
                        <i class="fab fa-weixin"></i>
                        <span>微信</span>
                        <div class="social-glow"></div>
                    </a>
                    <a href="#" class="social-link" data-platform="email">
                        <i class="fas fa-envelope"></i>
                        <span>邮箱</span>
                        <div class="social-glow"></div>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 个人简介. 保留所有权利.</p>
                    <p class="footer-subtitle">Powered by Advanced Technology</p>
                </div>
                <div class="footer-tech">
                    <div class="tech-indicator">
                        <div class="tech-dot"></div>
                        <span>系统运行正常</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 加载动画 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <div class="loading-text glitch" data-text="LOADING">LOADING</div>
            </div>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-percentage">0%</div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
