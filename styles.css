/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 默认赛博朋克主题 */
    --primary-color: #00f5ff;
    --secondary-color: #ff0080;
    --accent-color: #39ff14;
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-surface: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-accent: var(--accent-color);

    /* 科技感效果 */
    --neon-glow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    --neon-glow-strong: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor, 0 0 20px currentColor;
    --cyber-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
    --cyber-border: 1px solid rgba(0, 245, 255, 0.3);
    --hologram-glow: 0 0 30px rgba(0, 245, 255, 0.4), 0 0 60px rgba(0, 245, 255, 0.2);
    --scan-line: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.8), transparent);

    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-accent: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-cyber: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    --gradient-hologram: linear-gradient(45deg,
        rgba(0, 245, 255, 0.1) 0%,
        rgba(255, 0, 128, 0.1) 25%,
        rgba(57, 255, 20, 0.1) 50%,
        rgba(0, 245, 255, 0.1) 75%,
        rgba(255, 0, 128, 0.1) 100%);

    /* 动画时间 */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --animation-duration: 2s;

    /* 阴影效果 */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-cyber: 0 0 20px rgba(0, 245, 255, 0.3), 0 0 40px rgba(0, 245, 255, 0.1);

    /* 边框效果 */
    --border-color: rgba(255, 255, 255, 0.1);
    --border-glow: 1px solid rgba(0, 245, 255, 0.5);
}

body {
    font-family: 'Inter', 'Orbitron', 'Courier New', monospace;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    position: relative;
    transition: all var(--transition-normal);
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 50%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(57, 255, 20, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: ambient-glow 10s ease-in-out infinite alternate;
}

@keyframes ambient-glow {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

/* 粒子背景容器 */
#particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -2;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: float-particle 20s linear infinite;
    opacity: 0.6;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* 科技感装饰线条 */
.tech-lines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.tech-line {
    position: absolute;
    background: var(--gradient-primary);
    opacity: 0.3;
    animation: tech-line-glow 3s ease-in-out infinite alternate;
}

.tech-line-1 {
    top: 20%;
    left: 0;
    width: 100%;
    height: 1px;
    animation-delay: 0s;
}

.tech-line-2 {
    top: 0;
    right: 30%;
    width: 1px;
    height: 100%;
    animation-delay: 0.5s;
}

.tech-line-3 {
    bottom: 40%;
    left: 0;
    width: 100%;
    height: 1px;
    animation-delay: 1s;
}

.tech-line-4 {
    top: 0;
    left: 10%;
    width: 1px;
    height: 100%;
    animation-delay: 1.5s;
}

@keyframes tech-line-glow {
    0% { opacity: 0.1; }
    100% { opacity: 0.5; }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 科技感装饰框 */
.tech-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    pointer-events: none;
    z-index: -1;
}

.tech-corner {
    position: absolute;
    width: 30px;
    height: 30px;
    border: 2px solid var(--primary-color);
    opacity: 0.6;
    animation: corner-glow 2s ease-in-out infinite alternate;
}

.tech-corner-tl {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
}

.tech-corner-tr {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
}

.tech-corner-bl {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
}

.tech-corner-br {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
}

@keyframes corner-glow {
    0% {
        opacity: 0.3;
        box-shadow: none;
    }
    100% {
        opacity: 0.8;
        box-shadow: 0 0 10px var(--primary-color);
    }
}

/* 打字机效果 */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--primary-color);
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: var(--primary-color); }
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 20px 0;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    justify-content: center;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
}

.status-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-family: 'Orbitron', monospace;
}

/* 科技感文字效果 */
.cyber-text {
    background: var(--gradient-cyber);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.neon-text {
    color: var(--primary-color);
    text-shadow: var(--neon-glow);
    animation: neon-flicker 2s infinite alternate;
}

@keyframes neon-flicker {
    0%, 100% {
        text-shadow: var(--neon-glow);
        opacity: 1;
    }
    50% {
        text-shadow: var(--neon-glow-strong);
        opacity: 0.8;
    }
}

/* 故障效果 */
.glitch {
    position: relative;
    animation: glitch 3s infinite;
}

.glitch::before,
.glitch::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: inherit;
}

.glitch::before {
    animation: glitch-1 0.3s infinite linear alternate-reverse;
    color: var(--secondary-color);
    z-index: -1;
    clip-path: polygon(0 0, 100% 0, 100% 35%, 0 35%);
}

.glitch::after {
    animation: glitch-2 0.3s infinite linear alternate-reverse;
    color: var(--accent-color);
    z-index: -2;
    clip-path: polygon(0 65%, 100% 65%, 100% 100%, 0 100%);
}

@keyframes glitch {
    0%, 100% {
        transform: translate(0);
        filter: hue-rotate(0deg);
    }
    20% {
        transform: translate(-2px, 2px);
        filter: hue-rotate(90deg);
    }
    40% {
        transform: translate(-2px, -2px);
        filter: hue-rotate(180deg);
    }
    60% {
        transform: translate(2px, 2px);
        filter: hue-rotate(270deg);
    }
    80% {
        transform: translate(2px, -2px);
        filter: hue-rotate(360deg);
    }
}

@keyframes glitch-1 {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

@keyframes glitch-2 {
    0% { transform: translate(0); }
    20% { transform: translate(2px, -2px); }
    40% { transform: translate(2px, 2px); }
    60% { transform: translate(-2px, -2px); }
    80% { transform: translate(-2px, 2px); }
    100% { transform: translate(0); }
}

/* 全息投影效果 */
.hologram-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-hologram);
    opacity: 0.1;
    animation: hologram-flicker 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes hologram-flicker {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.3; }
}

/* 数字雨效果 */
.matrix-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    opacity: 0.1;
}

.matrix-char {
    position: absolute;
    color: var(--accent-color);
    font-family: 'Courier New', monospace;
    font-size: 14px;
    animation: matrix-fall 3s linear infinite;
}

@keyframes matrix-fall {
    0% {
        transform: translateY(-100px);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* 主题选择器 */
.theme-selector {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
    background: rgba(26, 26, 26, 0.95);
    border: var(--cyber-border);
    border-radius: 15px;
    padding: 15px;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-cyber);
    transition: all var(--transition-normal);
}

.theme-selector:hover {
    transform: translateY(-2px);
    box-shadow: var(--hologram-glow);
}

.theme-selector-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
}

.theme-selector-label i {
    color: var(--primary-color);
}

.theme-select {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(0, 245, 255, 0.3);
    color: var(--text-primary);
    font-family: 'Orbitron', monospace;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    width: 100%;
    transition: all var(--transition-normal);
}

.theme-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--cyber-shadow);
}

.theme-select option {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 5px;
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: var(--cyber-border);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* 主页部分 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="90" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-container {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.hero-avatar {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;
}

.avatar-container {
    position: relative;
    display: inline-block;
}

.avatar-img {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    border: 3px solid var(--primary-color);
    object-fit: cover;
    animation: avatar-pulse 3s ease-in-out infinite;
    position: relative;
    z-index: 2;
    filter: brightness(1.1) contrast(1.1);
}

@keyframes avatar-pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
    }
}

.avatar-ring {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: rotate 15s linear infinite;
    opacity: 0.7;
}

.avatar-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 245, 255, 0.2) 0%, transparent 70%);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

.avatar-scan {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.3), transparent);
    animation: scan-line 3s linear infinite;
    opacity: 0.8;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes glow-pulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.7; }
}

@keyframes scan-line {
    0% { transform: translateX(-100%) rotate(45deg); }
    100% { transform: translateX(300%) rotate(45deg); }
}

.greeting {
    display: block;
    font-size: 1.2rem;
    font-weight: 400;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.name {
    display: block;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 2rem;
}

/* 科技感按钮 */
.btn {
    padding: 12px 30px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    display: inline-block;
    position: relative;
    overflow: hidden;
    font-family: 'Orbitron', monospace;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cyber-btn {
    background: rgba(0, 0, 0, 0.5);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    position: relative;
    overflow: hidden;
}

.cyber-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--scan-line);
    transition: left 0.5s ease;
}

.cyber-btn:hover::before {
    left: 100%;
}

.cyber-btn:hover {
    color: var(--bg-primary);
    background: var(--primary-color);
    box-shadow: var(--cyber-shadow);
    transform: translateY(-2px);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.cyber-btn:hover .btn-glow {
    opacity: 0.2;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--hologram-glow);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-secondary:hover {
    background: rgba(0, 245, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: var(--cyber-shadow);
}

/* 科技感卡片 */
.cyber-card {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 15px;
    padding: 2rem;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gradient-primary);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

.cyber-card:hover::before {
    transform: translateX(0);
}

.cyber-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--cyber-shadow);
    transform: translateY(-5px);
}

.social-links {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 2rem;
}

.social-link {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
    font-size: 1.2rem;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.social-link i {
    position: relative;
    z-index: 2;
    transition: color var(--transition-normal);
}

.social-link:hover::before {
    opacity: 1;
}

.social-link:hover i {
    color: var(--bg-primary);
}

.social-link:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--hologram-glow);
    border-color: var(--accent-color);
}

.social-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(0, 245, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.social-link:hover .social-ripple {
    width: 100px;
    height: 100px;
    opacity: 0;
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

.scroll-arrow {
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.7);
    border-top: none;
    border-left: none;
    transform: rotate(45deg);
}

/* 通用部分样式 */
section {
    padding: 100px 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* 关于我部分 */
.about {
    background: var(--bg-secondary);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.bio-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    position: relative;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.stat-item:hover {
    transform: translateY(-10px);
}

.stat-icon {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: icon-float 3s ease-in-out infinite;
}

@keyframes icon-float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    font-family: 'Orbitron', monospace;
    position: relative;
}

.stat-number::after {
    content: '+';
    position: absolute;
    right: -15px;
    top: 0;
    font-size: 0.7em;
    opacity: 0.7;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    border-radius: inherit;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.stat-item:hover .stat-glow {
    opacity: 0.1;
}

/* 计数器动画 */
.counter {
    animation: counter-glow 2s ease-in-out infinite alternate;
}

@keyframes counter-glow {
    0% { text-shadow: 0 0 5px var(--primary-color); }
    100% { text-shadow: 0 0 15px var(--primary-color); }
}

/* 技能部分 */
.skills {
    background: var(--bg-primary);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.skill-category {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.skill-category:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.skill-category h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    text-align: center;
    position: relative;
}

.skill-category h3::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.skill-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.skill-item:hover {
    background: #f0f4f8;
    transform: translateX(5px);
}

.skill-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    width: 40px;
    text-align: center;
}

.skill-info {
    flex: 1;
}

.skill-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.skill-progress {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.skill-progress-bar {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.skill-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.skill-level {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-left: 1rem;
}

/* 经历部分 */
.experience {
    background: var(--bg-secondary);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
    border-radius: 2px;
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    width: 50%;
    padding: 0 2rem;
}

.timeline-item:nth-child(odd) {
    left: 0;
    text-align: right;
}

.timeline-item:nth-child(even) {
    left: 50%;
    text-align: left;
}

.timeline-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow-md);
    position: relative;
    transition: all 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.timeline-item:nth-child(odd) .timeline-content::after {
    content: '';
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    border: 15px solid transparent;
    border-left-color: white;
}

.timeline-item:nth-child(even) .timeline-content::after {
    content: '';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    border: 15px solid transparent;
    border-right-color: white;
}

.timeline-dot {
    position: absolute;
    top: 50%;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transform: translateY(-50%);
    border: 4px solid white;
    box-shadow: var(--shadow-md);
}

.timeline-item:nth-child(odd) .timeline-dot {
    right: -2.5rem;
}

.timeline-item:nth-child(even) .timeline-dot {
    left: -2.5rem;
}

.experience-company {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.experience-position {
    font-size: 1.1rem;
    font-weight: 600;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.experience-duration {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 1rem;
    font-weight: 500;
}

.experience-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 项目部分 */
.projects {
    background: var(--bg-primary);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.project-content {
    padding: 2rem;
}

.project-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-tech {
    display: inline-block;
    background: var(--gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.project-link:hover {
    color: var(--secondary-color);
    transform: translateX(5px);
}

.project-link i {
    margin-left: 0.5rem;
}

/* 联系部分 */
.contact {
    background: var(--bg-secondary);
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-right: 1rem;
}

.contact-item span {
    color: var(--text-secondary);
    font-weight: 500;
}

/* 联系表单 */
.contact-form-container {
    margin-top: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cyber-form {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-input {
    width: 100%;
    padding: 15px 0;
    background: transparent;
    border: none;
    border-bottom: 2px solid rgba(0, 245, 255, 0.3);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    transition: all var(--transition-normal);
}

.form-input:focus {
    outline: none;
    border-bottom-color: var(--primary-color);
}

.form-label {
    position: absolute;
    top: 15px;
    left: 0;
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all var(--transition-normal);
    pointer-events: none;
}

.form-input:focus + .form-label,
.form-input:not(:placeholder-shown) + .form-label {
    top: -10px;
    font-size: 0.8rem;
    color: var(--primary-color);
}

.form-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.form-input:focus ~ .form-line {
    width: 100%;
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
    padding-top: 15px;
}

/* 页脚 */
.footer {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 3rem 0 2rem;
    border-top: 1px solid rgba(0, 245, 255, 0.2);
    position: relative;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-text p {
    margin: 0.5rem 0;
}

.footer-subtitle {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.footer-tech {
    display: flex;
    align-items: center;
}

.tech-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 245, 255, 0.3);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.tech-dot {
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: status-pulse 2s ease-in-out infinite;
}

/* 加载动画 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.loading-logo {
    margin-bottom: 2rem;
}

.loading-text {
    font-size: 2rem;
    font-family: 'Orbitron', monospace;
    font-weight: 700;
    margin-bottom: 2rem;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: rgba(0, 245, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto 1rem;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    transition: width 0.3s ease;
    animation: loading-glow 2s ease-in-out infinite alternate;
}

@keyframes loading-glow {
    0% { box-shadow: 0 0 5px var(--primary-color); }
    100% { box-shadow: 0 0 20px var(--primary-color); }
}

.loading-percentage {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .tech-lines {
        display: none;
    }

    .particles-container {
        opacity: 0.5;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .theme-selector {
        top: 10px;
        right: 10px;
        padding: 10px;
    }

    .theme-selector-label {
        font-size: 0.8rem;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: left 0.3s ease;
        border-top: 1px solid rgba(0, 245, 255, 0.3);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-link {
        font-size: 1.2rem;
        padding: 1rem;
    }

    .hamburger {
        display: flex;
        flex-direction: column;
        cursor: pointer;
        z-index: 1001;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-content {
        padding: 0 1rem;
    }

    .avatar-img {
        width: 120px;
        height: 120px;
    }

    .name {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .btn {
        width: 200px;
        text-align: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-item {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .skill-category {
        padding: 1.5rem;
    }

    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        width: 100%;
        left: 0 !important;
        text-align: left;
        padding-left: 4rem;
        padding-right: 1rem;
    }

    .timeline-item .timeline-content::after {
        display: none;
    }

    .timeline-dot {
        left: 10px !important;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .project-card {
        margin: 0 1rem;
    }

    .contact-info {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-form-container {
        margin: 2rem 1rem 0;
    }

    .cyber-form {
        padding: 1.5rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .social-links {
        gap: 1rem;
    }

    .social-link {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    /* 禁用一些效果以提高性能 */
    .particles-container {
        display: none;
    }

    .matrix-rain {
        opacity: 0.05;
    }

    .tech-frame {
        display: none;
    }

    .hologram-overlay {
        opacity: 0.05;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .section-title {
        font-size: 2rem;
    }

    .name {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .avatar-img {
        width: 100px;
        height: 100px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-icon {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .theme-selector {
        position: relative;
        top: auto;
        right: auto;
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    /* 进一步简化效果 */
    .glitch,
    .neon-text,
    .cyber-text {
        animation: none;
        text-shadow: none;
    }

    .loading-screen {
        display: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #ffffff;
        --secondary-color: #000000;
        --accent-color: #ffff00;
        --bg-primary: #000000;
        --bg-secondary: #111111;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .particles-container,
    .matrix-rain,
    .hologram-overlay {
        display: none;
    }
}
