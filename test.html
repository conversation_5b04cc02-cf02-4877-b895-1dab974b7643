<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #0a0a0a;
            color: white;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 10px;
        }
        .theme-selector {
            margin: 20px 0;
        }
        select {
            padding: 10px;
            font-size: 16px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #4CAF50; }
        .error { background: #f44336; }
        .info { background: #2196F3; }
    </style>
</head>
<body>
    <h1>个人介绍页面测试</h1>
    
    <div class="test-section">
        <h2>JSON加载测试</h2>
        <div id="json-status" class="status info">正在加载...</div>
        <div id="json-content"></div>
    </div>
    
    <div class="test-section">
        <h2>主题选择器测试</h2>
        <div class="theme-selector">
            <label for="theme-select">选择主题：</label>
            <select id="theme-select">
                <option value="cyberpunk">赛博朋克</option>
                <option value="matrix">黑客帝国</option>
                <option value="neon">霓虹科技</option>
                <option value="quantum">量子蓝</option>
            </select>
        </div>
        <div id="theme-status" class="status info">等待选择主题...</div>
    </div>
    
    <div class="test-section">
        <h2>控制台日志</h2>
        <div id="console-log" style="background: #222; padding: 10px; font-family: monospace; max-height: 300px; overflow-y: auto;"></div>
    </div>

    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('console-log');
        
        function addLog(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#4ecdc4';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(div);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };
        
        // 测试JSON加载
        async function testJsonLoading() {
            try {
                console.log('开始测试JSON加载...');
                const response = await fetch('profile.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                console.log('JSON加载成功');
                
                document.getElementById('json-status').className = 'status success';
                document.getElementById('json-status').textContent = 'JSON加载成功';
                
                // 显示主题信息
                if (data.themes) {
                    const themeCount = Object.keys(data.themes).length;
                    document.getElementById('json-content').innerHTML = `
                        <p>找到 ${themeCount} 个主题:</p>
                        <ul>${Object.keys(data.themes).map(theme => `<li>${theme}: ${data.themes[theme].name}</li>`).join('')}</ul>
                    `;
                    console.log(`找到 ${themeCount} 个主题`);
                } else {
                    console.error('未找到主题数据');
                }
                
                return data;
            } catch (error) {
                console.error('JSON加载失败:', error.message);
                document.getElementById('json-status').className = 'status error';
                document.getElementById('json-status').textContent = `JSON加载失败: ${error.message}`;
                return null;
            }
        }
        
        // 测试主题切换
        function testThemeSelector(profileData) {
            const themeSelect = document.getElementById('theme-select');
            const themeStatus = document.getElementById('theme-status');
            
            if (!profileData || !profileData.themes) {
                themeStatus.className = 'status error';
                themeStatus.textContent = '无法测试主题切换：缺少主题数据';
                return;
            }
            
            themeSelect.addEventListener('change', function() {
                const selectedTheme = this.value;
                const theme = profileData.themes[selectedTheme];
                
                if (theme) {
                    themeStatus.className = 'status success';
                    themeStatus.textContent = `主题切换成功: ${theme.name}`;
                    console.log(`切换到主题: ${selectedTheme} (${theme.name})`);
                    
                    // 应用主题颜色到body
                    document.body.style.background = theme.colors.background;
                    document.body.style.color = theme.colors.text;
                } else {
                    themeStatus.className = 'status error';
                    themeStatus.textContent = `主题不存在: ${selectedTheme}`;
                    console.error(`主题不存在: ${selectedTheme}`);
                }
            });
            
            themeStatus.className = 'status success';
            themeStatus.textContent = '主题选择器已就绪';
            console.log('主题选择器初始化完成');
        }
        
        // 启动测试
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('页面加载完成，开始测试...');
            const profileData = await testJsonLoading();
            testThemeSelector(profileData);
        });
    </script>
</body>
</html>
