<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版 - 高科技个人介绍</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00f5ff;
            --secondary-color: #ff0080;
            --accent-color: #39ff14;
            --bg-primary: #0a0a0a;
            --bg-secondary: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Orbitron', monospace;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 50%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(57, 255, 20, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* 导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.98);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 245, 255, 0.3);
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.5);
        }
        
        .navbar.scrolled {
            background: rgba(10, 10, 10, 0.99);
            border-bottom: 1px solid var(--primary-color);
            box-shadow: 0 2px 30px rgba(0, 245, 255, 0.2);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 70px;
        }
        
        .nav-logo {
            display: flex;
            align-items: center;
            position: relative;
        }
        
        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Orbitron', monospace;
            text-shadow: 0 0 10px rgba(0, 245, 255, 0.5);
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-link {
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
            padding: 10px 15px;
            border-radius: 8px;
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }
        
        .nav-link:hover {
            color: var(--primary-color);
            background: rgba(0, 245, 255, 0.1);
            text-shadow: 0 0 10px var(--primary-color);
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 5px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            transition: width 0.3s ease;
        }
        
        .nav-link:hover::after,
        .nav-link.active::after {
            width: 80%;
        }
        
        .nav-link.active {
            color: var(--primary-color);
            background: rgba(0, 245, 255, 0.15);
        }
        
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 10px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .hamburger span {
            width: 25px;
            height: 3px;
            background: var(--primary-color);
            margin: 3px 0;
            transition: all 0.3s ease;
            border-radius: 2px;
            box-shadow: 0 0 5px rgba(0, 245, 255, 0.3);
        }
        
        /* 主题选择器 */
        .theme-selector {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(26, 26, 26, 0.95);
            border: 1px solid var(--primary-color);
            border-radius: 15px;
            padding: 15px;
            backdrop-filter: blur(20px);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }
        
        .theme-selector-label {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
            color: var(--text-primary);
            font-size: 0.9rem;
        }
        
        .theme-select {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid var(--primary-color);
            color: var(--text-primary);
            font-family: 'Orbitron', monospace;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            width: 100%;
        }
        
        /* 主页部分 */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }
        
        .avatar-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }
        
        .avatar-img {
            width: 180px;
            height: 180px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            object-fit: cover;
            animation: avatar-pulse 3s ease-in-out infinite;
        }
        
        @keyframes avatar-pulse {
            0%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
            }
            50% { 
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
            }
        }
        
        .name {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Orbitron', monospace;
        }
        
        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
        }
        
        .hero-description {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* 滚动指示器 */
        .scroll-indicator {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            color: var(--text-primary);
            opacity: 0.8;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { 
                transform: translateX(-50%) translateY(0); 
            }
            40% { 
                transform: translateX(-50%) translateY(-10px); 
            }
            60% { 
                transform: translateX(-50%) translateY(-5px); 
            }
        }
        
        .scroll-arrow {
            width: 24px;
            height: 24px;
            border: 2px solid var(--primary-color);
            border-top: none;
            border-left: none;
            transform: rotate(45deg);
            margin-bottom: 5px;
            animation: arrow-glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes arrow-glow {
            0% { 
                border-color: var(--primary-color);
                box-shadow: 0 0 5px var(--primary-color);
            }
            100% { 
                border-color: var(--accent-color);
                box-shadow: 0 0 15px var(--accent-color);
            }
        }
        
        .scroll-text {
            font-size: 0.9rem;
            font-family: 'Orbitron', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
            color: var(--text-secondary);
            text-align: center;
        }
        
        /* 通用部分 */
        .section {
            padding: 4rem 0;
        }
        
        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-family: 'Orbitron', monospace;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 70px);
                background: rgba(10, 10, 10, 0.98);
                backdrop-filter: blur(20px);
                flex-direction: column;
                justify-content: center;
                align-items: center;
                transition: left 0.3s ease;
                border-top: 1px solid rgba(0, 245, 255, 0.3);
                box-shadow: 0 5px 30px rgba(0, 0, 0, 0.5);
            }
            
            .nav-menu.active {
                left: 0;
            }
            
            .nav-menu li {
                margin: 1.5rem 0;
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.3s ease;
            }
            
            .nav-menu.active li {
                opacity: 1;
                transform: translateY(0);
            }
            
            .nav-link {
                font-size: 1.2rem;
                padding: 1.5rem 2rem;
                border: 1px solid transparent;
                border-radius: 10px;
                min-width: 200px;
                text-align: center;
                display: block;
            }
            
            .hamburger {
                display: flex;
            }
            
            .hamburger.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
                background: var(--accent-color);
            }
            
            .hamburger.active span:nth-child(2) {
                opacity: 0;
                transform: translateX(20px);
            }
            
            .hamburger.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -6px);
                background: var(--accent-color);
            }
            
            .name {
                font-size: 2.5rem;
            }
            
            .avatar-img {
                width: 120px;
                height: 120px;
            }
            
            .theme-selector {
                top: 10px;
                right: 10px;
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Portfolio</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">首页</a></li>
                <li><a href="#about" class="nav-link">关于我</a></li>
                <li><a href="#skills" class="nav-link">技能</a></li>
                <li><a href="#contact" class="nav-link">联系</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 主题选择器 -->
    <div class="theme-selector">
        <div class="theme-selector-label">
            <i class="fas fa-palette"></i>
            <span>主题</span>
        </div>
        <select id="theme-select" class="theme-select">
            <option value="cyberpunk">赛博朋克</option>
            <option value="matrix">黑客帝国</option>
            <option value="neon">霓虹科技</option>
            <option value="quantum">量子蓝</option>
        </select>
    </div>

    <!-- 主页部分 -->
    <section id="home" class="hero">
        <div class="container">
            <div class="avatar-container">
                <img id="avatar" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face" alt="头像" class="avatar-img">
            </div>
            <h1 id="name" class="name">张三</h1>
            <p id="title" class="hero-subtitle">全栈开发工程师</p>
            <p id="bio" class="hero-description">拥有5年以上全栈开发经验，专注于企业级应用开发和微服务架构设计</p>
        </div>
        
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
            <span class="scroll-text">向下滚动</span>
        </div>
    </section>

    <!-- 关于我部分 -->
    <section id="about" class="section">
        <div class="container">
            <h2 class="section-title">关于我</h2>
            <p style="text-align: center; font-size: 1.2rem; max-width: 800px; margin: 0 auto;">
                热爱技术创新，追求代码质量和用户体验的完美结合。在全栈开发领域有着丰富的经验，
                擅长使用现代化的技术栈构建高性能、可扩展的应用程序。
            </p>
        </div>
    </section>

    <!-- 技能部分 -->
    <section id="skills" class="section">
        <div class="container">
            <h2 class="section-title">技能专长</h2>
            <p style="text-align: center; font-size: 1.2rem;">
                精通多种编程语言和框架，具备完整的全栈开发能力
            </p>
        </div>
    </section>

    <!-- 联系部分 -->
    <section id="contact" class="section">
        <div class="container">
            <h2 class="section-title">联系我</h2>
            <div style="text-align: center;">
                <p style="font-size: 1.2rem; margin-bottom: 2rem;">欢迎与我联系，讨论技术或合作机会</p>
                <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-envelope" style="color: var(--primary-color);"></i>
                        <span><EMAIL></span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-phone" style="color: var(--primary-color);"></i>
                        <span>+86 138-0000-0000</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                        <span>北京, 中国</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // 主题数据
        const themes = {
            cyberpunk: {
                name: "赛博朋克",
                colors: {
                    primary: "#00f5ff",
                    secondary: "#ff0080",
                    accent: "#39ff14",
                    background: "#0a0a0a",
                    surface: "#1a1a1a",
                    text: "#ffffff",
                    textSecondary: "#b0b0b0"
                }
            },
            matrix: {
                name: "黑客帝国",
                colors: {
                    primary: "#00ff41",
                    secondary: "#008f11",
                    accent: "#39ff14",
                    background: "#000000",
                    surface: "#001100",
                    text: "#00ff41",
                    textSecondary: "#008f11"
                }
            },
            neon: {
                name: "霓虹科技",
                colors: {
                    primary: "#ff006e",
                    secondary: "#8338ec",
                    accent: "#3a86ff",
                    background: "#0d1117",
                    surface: "#161b22",
                    text: "#ffffff",
                    textSecondary: "#8b949e"
                }
            },
            quantum: {
                name: "量子蓝",
                colors: {
                    primary: "#00d4ff",
                    secondary: "#0099cc",
                    accent: "#66ffff",
                    background: "#001122",
                    surface: "#002244",
                    text: "#ffffff",
                    textSecondary: "#99ccff"
                }
            }
        };
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeThemeSelector();
            initializeNavigation();
        });
        
        // 主题切换
        function initializeThemeSelector() {
            const themeSelect = document.getElementById('theme-select');
            const savedTheme = localStorage.getItem('selectedTheme') || 'cyberpunk';
            
            themeSelect.value = savedTheme;
            applyTheme(savedTheme);
            
            themeSelect.addEventListener('change', function() {
                const themeName = this.value;
                applyTheme(themeName);
                localStorage.setItem('selectedTheme', themeName);
            });
        }
        
        function applyTheme(themeName) {
            const theme = themes[themeName];
            if (!theme) return;
            
            const root = document.documentElement;
            Object.entries(theme.colors).forEach(([key, value]) => {
                const cssVar = '--' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
                root.style.setProperty(cssVar, value);
            });
        }
        
        // 导航功能
        function initializeNavigation() {
            // 平滑滚动
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // 移动端菜单
            const hamburger = document.querySelector('.hamburger');
            const navMenu = document.querySelector('.nav-menu');
            
            if (hamburger && navMenu) {
                hamburger.addEventListener('click', function() {
                    navMenu.classList.toggle('active');
                    hamburger.classList.toggle('active');
                });
                
                // 点击菜单项后关闭菜单
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        navMenu.classList.remove('active');
                        hamburger.classList.remove('active');
                    });
                });
            }
            
            // 滚动时导航栏效果
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
                
                // 更新活动导航链接
                updateActiveNavLink();
            });
        }
        
        // 更新活动导航链接
        function updateActiveNavLink() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');
            
            let currentSection = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 100;
                const sectionHeight = section.offsetHeight;
                
                if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                    currentSection = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${currentSection}`) {
                    link.classList.add('active');
                }
            });
        }
    </script>
</body>
</html>
